export default function GameOverScreen({ milkshakesCompleted, achievement, onRestart }) {
  const getBadgeEmoji = (achievement) => {
    switch (achievement) {
      case 'Slurp Boss':
        return '👑';
      case 'Slurp Captain':
        return '🏆';
      default:
        return '🥤';
    }
  };

  const getEncouragementMessage = () => {
    if (milkshakesCompleted >= 10) {
      return "Incredible! You're a slurping legend! 🌟";
    } else if (milkshakesCompleted >= 5) {
      return "Amazing work! You're getting really good at this! 🎉";
    } else if (milkshakesCompleted >= 3) {
      return "Great job! Keep practicing your slurping technique! 💪";
    } else if (milkshakesCompleted >= 1) {
      return "Good start! Try making louder slurping sounds! 🔊";
    } else {
      return "Don't give up! Make sure to allow microphone access! 🎤";
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-3xl p-8 max-w-sm w-full text-center shadow-2xl">
        {/* Game Over Title */}
        <h2 className="text-3xl font-bold text-purple-800 mb-4">Game Over!</h2>
        
        {/* Siren Animation */}
        <div className="text-6xl mb-4 animate-pulse">🚨</div>
        
        {/* Score */}
        <div className="mb-6">
          <div className="text-5xl font-bold text-pink-600 mb-2">
            {milkshakesCompleted}
          </div>
          <div className="text-lg text-gray-600">
            Milkshake{milkshakesCompleted !== 1 ? 's' : ''} Completed
          </div>
        </div>

        {/* Achievement Badge */}
        {achievement && (
          <div className="mb-6 p-4 bg-gradient-to-r from-yellow-200 to-yellow-300 rounded-2xl border-2 border-yellow-400">
            <div className="text-4xl mb-2">{getBadgeEmoji(achievement)}</div>
            <div className="text-xl font-bold text-yellow-800">{achievement}</div>
            <div className="text-sm text-yellow-700">Achievement Unlocked!</div>
          </div>
        )}

        {/* Encouragement Message */}
        <div className="mb-6 p-3 bg-blue-50 rounded-lg">
          <p className="text-blue-800 text-sm">{getEncouragementMessage()}</p>
        </div>

        {/* Achievement Progress */}
        <div className="mb-6 space-y-2">
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">🏆 Slurp Captain (5)</span>
            <span className={milkshakesCompleted >= 5 ? 'text-green-600 font-bold' : 'text-gray-400'}>
              {milkshakesCompleted >= 5 ? '✓' : `${milkshakesCompleted}/5`}
            </span>
          </div>
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">👑 Slurp Boss (10)</span>
            <span className={milkshakesCompleted >= 10 ? 'text-green-600 font-bold' : 'text-gray-400'}>
              {milkshakesCompleted >= 10 ? '✓' : `${milkshakesCompleted}/10`}
            </span>
          </div>
        </div>

        {/* Restart Button */}
        <button
          onClick={onRestart}
          className="bg-pink-500 hover:bg-pink-600 text-white font-bold py-3 px-6 rounded-full text-lg shadow-lg transform transition hover:scale-105 w-full"
        >
          🥤 Play Again!
        </button>

        {/* Tips */}
        <div className="mt-4 text-xs text-gray-500">
          💡 Tip: Make loud, continuous slurping sounds for best results!
        </div>
      </div>
    </div>
  );
}
