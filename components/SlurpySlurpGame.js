import { useState, useEffect, useRef } from "react";
import MilkshakeGlass from "./MilkshakeGlass";
import Timer from "./Timer";
import ScoreDisplay from "./ScoreDisplay";
import GameOverScreen from "./GameOverScreen";
import AudioProcessor from "./AudioProcessor";
import soundManager from "./SoundManager";

const GAME_DURATION = 10; // 10 seconds
const SLURP_CAPTAIN_THRESHOLD = 5;
const SLURP_BOSS_THRESHOLD = 10;

export default function SlurpySlurpGame() {
  const [gameState, setGameState] = useState("waiting"); // 'waiting', 'playing', 'gameOver'
  const [timeLeft, setTimeLeft] = useState(GAME_DURATION);
  const [milkshakesCompleted, setMilkshakesCompleted] = useState(0);
  const [currentMilkshakeLevel, setCurrentMilkshakeLevel] = useState(100);
  const [isSlurping, setIsSlurping] = useState(false);
  const [achievement, setAchievement] = useState(null);
  const [showAchievementNotification, setShowAchievementNotification] =
    useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [microphoneError, setMicrophoneError] = useState(null);
  const [isRefilling, setIsRefilling] = useState(false);

  const audioProcessorRef = useRef(null);
  const gameTimerRef = useRef(null);

  // Start game
  const startGame = async () => {
    setIsLoading(true);
    setMicrophoneError(null);

    try {
      // Initialize sound manager with user interaction
      await soundManager.initialize();

      setGameState("playing");
      setTimeLeft(GAME_DURATION);
      setMilkshakesCompleted(0);
      setCurrentMilkshakeLevel(100);
      setAchievement(null);
      setIsRefilling(false);

      // Start timer
      gameTimerRef.current = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            endGame();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      console.error("Error starting game:", error);
      setMicrophoneError(
        "Failed to initialize audio. The game will work in demo mode."
      );
    } finally {
      setIsLoading(false);
    }
  };

  // End game
  const endGame = () => {
    setGameState("gameOver");
    if (gameTimerRef.current) {
      clearInterval(gameTimerRef.current);
    }

    // Determine achievement
    if (milkshakesCompleted >= SLURP_BOSS_THRESHOLD) {
      setAchievement("Slurp Boss");
    } else if (milkshakesCompleted >= SLURP_CAPTAIN_THRESHOLD) {
      setAchievement("Slurp Captain");
    }

    // Play siren sound
    playSound("siren");
  };

  // Handle slurping input
  const handleSlurpInput = (intensity) => {
    if (gameState !== "playing") return;

    setIsSlurping(intensity > 0);

    if (intensity > 0) {
      setCurrentMilkshakeLevel((prev) => {
        // Adjust drain rate based on intensity (more responsive)
        const drainRate = intensity * 3; // Increased from 2 to 3 for faster gameplay
        const newLevel = Math.max(0, prev - drainRate);

        // If milkshake is finished (and not already refilling)
        if (newLevel === 0 && prev > 0 && !isRefilling) {
          setIsRefilling(true);

          setMilkshakesCompleted((count) => {
            const newCount = count + 1;
            // Play different sounds based on achievement milestones
            if (newCount === SLURP_CAPTAIN_THRESHOLD) {
              playSound("achievement");
              setShowAchievementNotification("Slurp Captain");
              setTimeout(() => setShowAchievementNotification(null), 3000);
            } else if (newCount === SLURP_BOSS_THRESHOLD) {
              playSound("achievement");
              setShowAchievementNotification("Slurp Boss");
              setTimeout(() => setShowAchievementNotification(null), 3000);
            } else {
              playSound("buzz");
            }
            return newCount;
          });

          // Reset to full milkshake after 1 second pause with ding sound
          setTimeout(() => {
            playSound("ding");
            setTimeout(() => {
              setCurrentMilkshakeLevel(100);
              setIsRefilling(false); // Reset refilling flag
            }, 100); // Small delay after ding sound
          }, 1000); // 1 second pause
        }

        return newLevel;
      });
    }
  };

  // Play sound effects
  const playSound = async (type) => {
    try {
      await soundManager.playSound(type);
    } catch (error) {
      console.log(`Playing ${type} sound (audio not available)`);
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (gameTimerRef.current) {
        clearInterval(gameTimerRef.current);
      }
    };
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gradient-to-b from-pink-100 to-purple-200 select-none">
      <div className="w-full max-w-md mx-auto touch-manipulation">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-black mb-2">SlurpySlurp</h1>
          <p className="text-gray-800">Drink as many milkshakes as you can!</p>
        </div>

        {/* Game Area */}
        <div className="bg-white rounded-3xl shadow-2xl p-6 mb-6">
          {/* Timer and Score */}
          <div className="flex justify-between items-center mb-6">
            <Timer timeLeft={timeLeft} isActive={gameState === "playing"} />
            <ScoreDisplay count={milkshakesCompleted} />
          </div>

          {/* Milkshake Glass */}
          <div className="flex justify-center mb-6">
            <MilkshakeGlass
              level={currentMilkshakeLevel}
              isSlurping={isSlurping}
            />
          </div>

          {/* Game Controls */}
          {gameState === "waiting" && (
            <div className="text-center">
              <button
                onClick={startGame}
                disabled={isLoading}
                className={`font-bold py-4 px-8 rounded-full text-xl shadow-lg transform transition touch-manipulation ${
                  isLoading
                    ? "bg-gray-400 text-gray-600 cursor-not-allowed"
                    : "bg-pink-500 hover:bg-pink-600 active:bg-pink-700 text-white hover:scale-105 active:scale-95"
                }`}
              >
                {isLoading ? (
                  <>
                    <span className="animate-spin inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                    Loading...
                  </>
                ) : (
                  "🥤 Start Slurping!"
                )}
              </button>
              <p className="text-black mt-4 text-sm">
                Tap to start, then make slurping sounds into your microphone!
              </p>
              {microphoneError && (
                <div className="mt-4 p-3 bg-yellow-100 border border-yellow-300 rounded-lg">
                  <p className="text-yellow-800 text-sm">
                    ⚠️ {microphoneError}
                  </p>
                </div>
              )}
            </div>
          )}

          {gameState === "playing" && (
            <div className="text-center">
              <div className="bg-green-100 border border-green-300 rounded-lg p-4">
                <p className="text-green-800 font-semibold">
                  🎤 Make slurping sounds to drink!
                </p>
                <div className="mt-2">
                  <div
                    className={`inline-block w-4 h-4 rounded-full ${
                      isSlurping ? "bg-green-500 animate-pulse" : "bg-gray-300"
                    }`}
                  ></div>
                  <span className="ml-2 text-sm text-gray-600">
                    {isSlurping ? "Slurping detected!" : "Listening..."}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Audio Processor */}
        {gameState === "playing" && (
          <AudioProcessor
            ref={audioProcessorRef}
            onSlurpDetected={handleSlurpInput}
          />
        )}

        {/* Achievement Notification */}
        {showAchievementNotification && (
          <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 animate-bounce">
            <div className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white px-6 py-3 rounded-full shadow-2xl border-2 border-yellow-300">
              <div className="flex items-center space-x-2">
                <span className="text-2xl">
                  {showAchievementNotification === "Slurp Boss" ? "👑" : "🏆"}
                </span>
                <div>
                  <div className="font-bold text-lg">
                    {showAchievementNotification}
                  </div>
                  <div className="text-sm opacity-90">
                    Achievement Unlocked!
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Game Over Screen */}
        {gameState === "gameOver" && (
          <GameOverScreen
            milkshakesCompleted={milkshakesCompleted}
            achievement={achievement}
            onRestart={startGame}
          />
        )}
      </div>
    </div>
  );
}
