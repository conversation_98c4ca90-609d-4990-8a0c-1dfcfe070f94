import { useEffect, useState } from 'react';

export default function MilkshakeGlass({ level, isSlurping }) {
  const [animatedLevel, setAnimatedLevel] = useState(level);

  // Smooth animation for level changes
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedLevel(level);
    }, 50);
    return () => clearTimeout(timer);
  }, [level]);

  // Calculate milkshake height based on level (0-100)
  const milkshakeHeight = Math.max(0, (animatedLevel / 100) * 200);
  
  // Determine milkshake color based on level for variety
  const getMilkshakeColor = () => {
    if (level > 80) return 'from-pink-300 to-pink-500';
    if (level > 60) return 'from-purple-300 to-purple-500';
    if (level > 40) return 'from-blue-300 to-blue-500';
    if (level > 20) return 'from-green-300 to-green-500';
    return 'from-yellow-300 to-yellow-500';
  };

  return (
    <div className="relative">
      {/* Glass Container */}
      <div className="relative w-32 h-64 mx-auto">
        {/* Glass outline */}
        <div className="absolute inset-0 border-4 border-gray-300 rounded-b-3xl bg-white bg-opacity-20 backdrop-blur-sm">
          {/* Glass shine effect */}
          <div className="absolute left-2 top-4 w-4 h-16 bg-white bg-opacity-30 rounded-full blur-sm"></div>
        </div>

        {/* Milkshake liquid */}
        <div 
          className="absolute bottom-0 left-0 right-0 rounded-b-3xl transition-all duration-300 ease-out"
          style={{ 
            height: `${milkshakeHeight}px`,
            background: `linear-gradient(to bottom, var(--tw-gradient-stops))`,
          }}
        >
          <div className={`w-full h-full bg-gradient-to-b ${getMilkshakeColor()} rounded-b-3xl relative overflow-hidden`}>
            {/* Milkshake surface bubbles */}
            {level > 0 && (
              <div className="absolute top-0 left-0 right-0 h-4">
                <div className="absolute top-1 left-4 w-2 h-2 bg-white bg-opacity-60 rounded-full animate-pulse"></div>
                <div className="absolute top-2 right-6 w-1 h-1 bg-white bg-opacity-40 rounded-full animate-pulse delay-100"></div>
                <div className="absolute top-1 left-1/2 w-1.5 h-1.5 bg-white bg-opacity-50 rounded-full animate-pulse delay-200"></div>
              </div>
            )}
            
            {/* Slurping animation effect */}
            {isSlurping && level > 0 && (
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2">
                <div className="w-1 h-8 bg-white bg-opacity-30 animate-pulse"></div>
              </div>
            )}
          </div>
        </div>

        {/* Straw */}
        <div className="absolute -top-8 left-1/2 transform -translate-x-1/2">
          {/* Straw bend */}
          <div className="relative">
            <div className="w-2 h-12 bg-red-400 rounded-t-full"></div>
            <div className="absolute -top-2 -right-4 w-6 h-2 bg-red-400 rounded-full transform rotate-45"></div>
            <div className="absolute -top-4 -right-8 w-2 h-8 bg-red-400 rounded-full"></div>
          </div>
        </div>

        {/* Glass rim */}
        <div className="absolute -top-2 left-0 right-0 h-4 border-2 border-gray-300 rounded-full bg-white bg-opacity-50"></div>
      </div>

      {/* Level indicator */}
      <div className="mt-4 text-center">
        <div className="bg-gray-200 rounded-full h-2 w-32 mx-auto overflow-hidden">
          <div 
            className="bg-pink-500 h-full transition-all duration-300 ease-out"
            style={{ width: `${level}%` }}
          ></div>
        </div>
        <p className="text-sm text-gray-600 mt-1">{Math.round(level)}%</p>
      </div>

      {/* Empty glass message */}
      {level === 0 && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-yellow-100 border border-yellow-300 rounded-lg p-2 text-yellow-800 text-sm font-semibold animate-bounce">
            🎉 Milkshake Complete!
          </div>
        </div>
      )}
    </div>
  );
}
