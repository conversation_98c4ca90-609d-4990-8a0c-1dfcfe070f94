import { useEffect, useState } from 'react';

export default function ScoreDisplay({ count }) {
  const [animatedCount, setAnimatedCount] = useState(count);
  const [isIncreasing, setIsIncreasing] = useState(false);

  useEffect(() => {
    if (count > animatedCount) {
      setIsIncreasing(true);
      const timer = setTimeout(() => {
        setAnimatedCount(count);
        setTimeout(() => setIsIncreasing(false), 300);
      }, 100);
      return () => clearTimeout(timer);
    } else {
      setAnimatedCount(count);
    }
  }, [count, animatedCount]);

  return (
    <div className="flex flex-col items-center">
      <div className="text-sm text-gray-600 mb-1">Milkshakes</div>
      <div className={`text-3xl font-bold text-purple-600 transition-transform duration-300 ${
        isIncreasing ? 'scale-125' : 'scale-100'
      }`}>
        {animatedCount}
      </div>
      {isIncreasing && (
        <div className="text-green-500 text-sm font-semibold animate-bounce">
          +1! 🥤
        </div>
      )}
    </div>
  );
}
