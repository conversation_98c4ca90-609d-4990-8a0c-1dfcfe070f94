export default function Timer({ timeLeft, isActive }) {
  const formatTime = (seconds) => {
    return seconds.toString().padStart(2, '0');
  };

  const getTimerColor = () => {
    if (timeLeft <= 3) return 'text-red-600 animate-pulse';
    if (timeLeft <= 5) return 'text-orange-600';
    return 'text-green-600';
  };

  return (
    <div className="flex flex-col items-center">
      <div className="text-sm text-gray-600 mb-1">Time Left</div>
      <div className={`text-3xl font-bold ${getTimerColor()}`}>
        {formatTime(timeLeft)}s
      </div>
      {isActive && (
        <div className="w-12 h-1 bg-gray-200 rounded-full mt-2 overflow-hidden">
          <div 
            className="h-full bg-blue-500 transition-all duration-1000 ease-linear"
            style={{ width: `${(timeLeft / 10) * 100}%` }}
          ></div>
        </div>
      )}
    </div>
  );
}
