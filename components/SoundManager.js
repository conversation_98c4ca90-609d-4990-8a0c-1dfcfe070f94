class SoundManager {
  constructor() {
    this.audioContext = null;
    this.sounds = {};
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;
    
    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      
      // Resume audio context if suspended
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }
      
      // Generate sound effects
      this.generateSounds();
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize sound manager:', error);
    }
  }

  generateSounds() {
    // Generate buzz sound (milkshake completion)
    this.sounds.buzz = this.generateBuzzSound();
    
    // Generate siren sound (game over)
    this.sounds.siren = this.generateSirenSound();
    
    // Generate achievement sound
    this.sounds.achievement = this.generateAchievementSound();
    
    // Generate slurp feedback sound
    this.sounds.slurp = this.generateSlurpSound();
  }

  generateBuzzSound() {
    const duration = 0.5;
    const sampleRate = this.audioContext.sampleRate;
    const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
    const data = buffer.getChannelData(0);

    for (let i = 0; i < buffer.length; i++) {
      const t = i / sampleRate;
      // Create a buzzing sound with frequency modulation
      const frequency = 200 + Math.sin(t * 50) * 50;
      data[i] = Math.sin(2 * Math.PI * frequency * t) * Math.exp(-t * 3) * 0.3;
    }

    return buffer;
  }

  generateSirenSound() {
    const duration = 2;
    const sampleRate = this.audioContext.sampleRate;
    const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
    const data = buffer.getChannelData(0);

    for (let i = 0; i < buffer.length; i++) {
      const t = i / sampleRate;
      // Create a siren sound with alternating frequencies
      const frequency = 400 + Math.sin(t * 8) * 200;
      data[i] = Math.sin(2 * Math.PI * frequency * t) * Math.exp(-t * 0.5) * 0.2;
    }

    return buffer;
  }

  generateAchievementSound() {
    const duration = 1;
    const sampleRate = this.audioContext.sampleRate;
    const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
    const data = buffer.getChannelData(0);

    for (let i = 0; i < buffer.length; i++) {
      const t = i / sampleRate;
      // Create a celebratory ascending tone
      const frequency = 440 + t * 220; // Rising from 440Hz to 660Hz
      const envelope = Math.exp(-t * 2);
      data[i] = Math.sin(2 * Math.PI * frequency * t) * envelope * 0.3;
      
      // Add harmonics for richness
      data[i] += Math.sin(2 * Math.PI * frequency * 2 * t) * envelope * 0.1;
      data[i] += Math.sin(2 * Math.PI * frequency * 3 * t) * envelope * 0.05;
    }

    return buffer;
  }

  generateSlurpSound() {
    const duration = 0.3;
    const sampleRate = this.audioContext.sampleRate;
    const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
    const data = buffer.getChannelData(0);

    for (let i = 0; i < buffer.length; i++) {
      const t = i / sampleRate;
      // Create a subtle slurping feedback sound
      const noise = (Math.random() - 0.5) * 2;
      const filtered = noise * Math.exp(-t * 10);
      data[i] = filtered * 0.1;
    }

    return buffer;
  }

  async playSound(soundName, volume = 1) {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.audioContext || !this.sounds[soundName]) {
      console.warn(`Sound ${soundName} not available`);
      return;
    }

    try {
      const source = this.audioContext.createBufferSource();
      const gainNode = this.audioContext.createGain();
      
      source.buffer = this.sounds[soundName];
      gainNode.gain.value = volume;
      
      source.connect(gainNode);
      gainNode.connect(this.audioContext.destination);
      
      source.start();
    } catch (error) {
      console.error(`Error playing sound ${soundName}:`, error);
    }
  }

  // Play sound with user interaction (required by some browsers)
  async playSoundWithInteraction(soundName, volume = 1) {
    // This should be called from a user interaction event
    if (!this.initialized) {
      await this.initialize();
    }
    
    await this.playSound(soundName, volume);
  }
}

// Create singleton instance
const soundManager = new SoundManager();

export default soundManager;
