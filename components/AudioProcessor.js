import { useEffect, useRef, forwardRef, useImperativeHandle } from 'react';

const AudioProcessor = forwardRef(({ onSlurpDetected }, ref) => {
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const microphoneRef = useRef(null);
  const animationFrameRef = useRef(null);

  useImperativeHandle(ref, () => ({
    startListening,
    stopListening
  }));

  const startListening = async () => {
    try {
      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // Create audio context and analyser
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      analyserRef.current = audioContextRef.current.createAnalyser();
      microphoneRef.current = audioContextRef.current.createMediaStreamSource(stream);
      
      // Configure analyser
      analyserRef.current.fftSize = 256;
      analyserRef.current.smoothingTimeConstant = 0.8;
      
      // Connect microphone to analyser
      microphoneRef.current.connect(analyserRef.current);
      
      // Start analyzing audio
      analyzeAudio();
      
    } catch (error) {
      console.error('Error accessing microphone:', error);
      // For now, simulate slurping for testing
      simulateSlurping();
    }
  };

  const analyzeAudio = () => {
    if (!analyserRef.current) return;

    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    
    const processAudio = () => {
      analyserRef.current.getByteFrequencyData(dataArray);
      
      // Calculate volume (RMS)
      let sum = 0;
      for (let i = 0; i < bufferLength; i++) {
        sum += dataArray[i] * dataArray[i];
      }
      const volume = Math.sqrt(sum / bufferLength);
      
      // Detect slurping sounds (looking for specific frequency patterns)
      const lowFreqEnergy = dataArray.slice(0, 32).reduce((a, b) => a + b, 0) / 32;
      const midFreqEnergy = dataArray.slice(32, 96).reduce((a, b) => a + b, 0) / 64;
      
      // Simple slurping detection algorithm
      const isSlurpingSound = volume > 30 && lowFreqEnergy > 40 && midFreqEnergy > 20;
      const intensity = isSlurpingSound ? Math.min(volume / 100, 1) : 0;
      
      onSlurpDetected(intensity);
      
      animationFrameRef.current = requestAnimationFrame(processAudio);
    };
    
    processAudio();
  };

  // Temporary simulation for testing without microphone
  const simulateSlurping = () => {
    let isSimulating = false;
    
    const simulate = () => {
      // Randomly simulate slurping every 2-4 seconds
      setTimeout(() => {
        if (!isSimulating) {
          isSimulating = true;
          const duration = 1000 + Math.random() * 2000; // 1-3 seconds
          const intensity = 0.3 + Math.random() * 0.7; // 0.3-1.0
          
          const startTime = Date.now();
          const simulateFrame = () => {
            const elapsed = Date.now() - startTime;
            if (elapsed < duration) {
              const currentIntensity = intensity * (1 - elapsed / duration);
              onSlurpDetected(currentIntensity);
              requestAnimationFrame(simulateFrame);
            } else {
              onSlurpDetected(0);
              isSimulating = false;
              simulate(); // Schedule next simulation
            }
          };
          simulateFrame();
        }
      }, 2000 + Math.random() * 2000);
    };
    
    simulate();
  };

  const stopListening = () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    
    if (audioContextRef.current) {
      audioContextRef.current.close();
    }
  };

  useEffect(() => {
    startListening();
    
    return () => {
      stopListening();
    };
  }, []);

  return null; // This component doesn't render anything
});

AudioProcessor.displayName = 'AudioProcessor';

export default AudioProcessor;
