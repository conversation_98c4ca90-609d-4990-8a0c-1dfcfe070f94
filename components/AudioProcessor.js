import { useEffect, useRef, forwardRef, useImperativeHandle } from "react";

const AudioProcessor = forwardRef(({ onSlurpDetected }, ref) => {
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const microphoneRef = useRef(null);
  const animationFrameRef = useRef(null);

  useImperativeHandle(ref, () => ({
    startListening,
    stopListening,
  }));

  const startListening = async () => {
    try {
      // Check if getUserMedia is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("getUserMedia not supported");
      }

      // Request microphone access with specific constraints
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
          sampleRate: 44100,
        },
      });

      // Create audio context and analyser
      audioContextRef.current = new (window.AudioContext ||
        window.webkitAudioContext)();

      // Resume audio context if it's suspended (required by some browsers)
      if (audioContextRef.current.state === "suspended") {
        await audioContextRef.current.resume();
      }

      analyserRef.current = audioContextRef.current.createAnalyser();
      microphoneRef.current =
        audioContextRef.current.createMediaStreamSource(stream);

      // Configure analyser for better slurping detection
      analyserRef.current.fftSize = 512; // Higher resolution for better frequency analysis
      analyserRef.current.smoothingTimeConstant = 0.3; // Less smoothing for more responsive detection
      analyserRef.current.minDecibels = -90;
      analyserRef.current.maxDecibels = -10;

      // Connect microphone to analyser
      microphoneRef.current.connect(analyserRef.current);

      // Start analyzing audio
      analyzeAudio();

      console.log(
        "Microphone access granted, listening for slurping sounds..."
      );
    } catch (error) {
      console.error("Error accessing microphone:", error);
      console.log("Falling back to simulation mode for testing...");
      // For now, simulate slurping for testing
      simulateSlurping();
    }
  };

  const analyzeAudio = () => {
    if (!analyserRef.current) return;

    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    const timeDataArray = new Uint8Array(analyserRef.current.fftSize);

    const processAudio = () => {
      analyserRef.current.getByteFrequencyData(dataArray);
      analyserRef.current.getByteTimeDomainData(timeDataArray);

      // Calculate volume (RMS)
      let sum = 0;
      for (let i = 0; i < bufferLength; i++) {
        sum += dataArray[i] * dataArray[i];
      }
      const volume = Math.sqrt(sum / bufferLength);

      // Calculate zero crossing rate for detecting turbulent sounds like slurping
      let zeroCrossings = 0;
      for (let i = 1; i < timeDataArray.length; i++) {
        if ((timeDataArray[i] - 128) * (timeDataArray[i - 1] - 128) < 0) {
          zeroCrossings++;
        }
      }
      const zeroCrossingRate = zeroCrossings / timeDataArray.length;

      // Analyze frequency bands for slurping characteristics
      const lowFreqEnergy =
        dataArray.slice(0, 16).reduce((a, b) => a + b, 0) / 16; // 0-1.4kHz
      const midFreqEnergy =
        dataArray.slice(16, 64).reduce((a, b) => a + b, 0) / 48; // 1.4-5.6kHz
      const highFreqEnergy =
        dataArray.slice(64, 128).reduce((a, b) => a + b, 0) / 64; // 5.6-11.2kHz

      // Slurping sound characteristics:
      // - Moderate to high volume
      // - High zero crossing rate (turbulent)
      // - Energy distributed across low and mid frequencies
      // - Less energy in very high frequencies
      const volumeThreshold = volume > 25;
      const turbulenceThreshold = zeroCrossingRate > 0.1;
      const frequencyPattern =
        lowFreqEnergy > 30 &&
        midFreqEnergy > 20 &&
        highFreqEnergy < midFreqEnergy;

      const isSlurpingSound =
        volumeThreshold && turbulenceThreshold && frequencyPattern;

      // Calculate intensity based on volume and frequency characteristics
      let intensity = 0;
      if (isSlurpingSound) {
        const volumeIntensity = Math.min(volume / 80, 1);
        const frequencyIntensity = Math.min(
          (lowFreqEnergy + midFreqEnergy) / 120,
          1
        );
        intensity = (volumeIntensity + frequencyIntensity) / 2;
      }

      onSlurpDetected(intensity);

      animationFrameRef.current = requestAnimationFrame(processAudio);
    };

    processAudio();
  };

  // Temporary simulation for testing without microphone
  const simulateSlurping = () => {
    let isSimulating = false;

    const simulate = () => {
      // Randomly simulate slurping every 2-4 seconds
      setTimeout(() => {
        if (!isSimulating) {
          isSimulating = true;
          const duration = 1000 + Math.random() * 2000; // 1-3 seconds
          const intensity = 0.3 + Math.random() * 0.7; // 0.3-1.0

          const startTime = Date.now();
          const simulateFrame = () => {
            const elapsed = Date.now() - startTime;
            if (elapsed < duration) {
              const currentIntensity = intensity * (1 - elapsed / duration);
              onSlurpDetected(currentIntensity);
              requestAnimationFrame(simulateFrame);
            } else {
              onSlurpDetected(0);
              isSimulating = false;
              simulate(); // Schedule next simulation
            }
          };
          simulateFrame();
        }
      }, 2000 + Math.random() * 2000);
    };

    simulate();
  };

  const stopListening = () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
    }
  };

  useEffect(() => {
    startListening();

    return () => {
      stopListening();
    };
  }, []);

  return null; // This component doesn't render anything
});

AudioProcessor.displayName = "AudioProcessor";

export default AudioProcessor;
